const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const db = require('./db');
const session = require('express-session');
const path = require('path');

// Middleware imports
const {
  generalLimiter,
  strictLimiter,
  adminLimiter,
  adminLoginLimiter,
  ratingLimiter,
  testLimiter
} = require('./middleware/rateLimiter');

const {
  handleValidationErrors,
  validateId,
  validateProductId,
  validateAdmin,
  validateBrand,
  validateCategory,
  validateProduct,
  validateVariant,
  validatePhoto,
  validateRating,
  validateBanner
} = require('./middleware/validation');

const { requireAdmin, optionalAdmin } = require('./middleware/auth');
const { uploadSingle, uploadMultiple } = require('./middleware/imageUpload');

// Import CRUD modules - organized by domain
const adminCRUD = require('./database/adminCRUD');
const adminSession = require('./database/adminSession');
const brandCRUD = require('./database/brandCRUD');
const categoryCRUD = require('./database/categoryCRUD');
const productCRUD = require('./database/productCRUD');
const variantCRUD = require('./database/variantCRUD');
const photoCRUD = require('./database/photoCRUD');
const brandPhotoCRUD = require('./database/brandPhotoCRUD');
const categoryPhotoCRUD = require('./database/categoryPhotoCRUD');
const ratingCRUD = require('./database/ratingCRUD');
const bannerCRUD = require('./database/bannerCRUD');

require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// ===== MIDDLEWARE SETUP =====

// Security and performance middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"]
    }
  }
}));

app.use(compression()); // Enable gzip compression

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  credentials: true,
  optionsSuccessStatus: 200 // Support legacy browsers
}));

// Session configuration with better security
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false, // Don't create sessions for unauthenticated users
  name: 'gg-catalog-session', // Custom session name
  cookie: {
    maxAge: 1000 * 60 * 60 * 24, // 24 hours
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // Use secure in production
    sameSite: 'strict'
  }
}));

// Apply general rate limiting after session middleware
app.use(generalLimiter);

// Body parsing middleware with size limits
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    // Add request body validation here if needed
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve uploaded images statically with caching
app.use('/uploads', express.static(path.join(__dirname, 'uploads'), {
  maxAge: '1d', // Cache images for 1 day
  etag: true,
  lastModified: true
}));

// ===== UTILITY ROUTES =====

// Enhanced health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'GG Catalog API',
    version: process.env.API_VERSION || '1.0.0',
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Test database connection with connection pool info
app.get('/api/test-db', testLimiter, async (req, res) => {
  try {
    const startTime = Date.now();
    const [rows] = await db.execute('SELECT 1 as test, NOW() as timestamp');
    const responseTime = Date.now() - startTime;
    
    res.json({
      success: true,
      message: 'Database connection successful',
      data: rows,
      responseTime: `${responseTime}ms`
    });
  } catch (error) {
    console.error('Database connection test failed:', error);
    res.status(500).json({
      success: false,
      error: 'Database connection failed',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// ===== ROUTE GROUPS =====

// Create Express routers for better organization
const adminRouter = express.Router();
const brandRouter = express.Router();
const categoryRouter = express.Router();
const productRouter = express.Router();
const uploadRouter = express.Router();

// ===== ADMIN ROUTES =====
adminRouter.post('/login', adminLoginLimiter, adminSession.adminLogin);
adminRouter.post('/logout', adminLimiter, adminSession.adminLogout);

// Check admin authentication status - optimized without rate limiting
adminRouter.get('/check', requireAdmin, (req, res) => {
  res.json({
    success: true,
    data: {
      id: req.admin.id,
      username: req.admin.username,
      lastLogin: req.admin.lastLogin
    },
    message: 'Admin is authenticated'
  });
});

adminRouter.get('/', adminLimiter, requireAdmin, adminCRUD.getAllAdmins);
adminRouter.post('/', adminLimiter, requireAdmin, validateAdmin, handleValidationErrors, adminCRUD.createAdmin);

// ===== BRAND ROUTES =====
brandRouter.get('/', brandCRUD.getAllBrands);
brandRouter.get('/:id', validateId, handleValidationErrors, brandCRUD.getBrandById);
brandRouter.post('/', strictLimiter, requireAdmin, validateBrand, handleValidationErrors, brandCRUD.createBrand);
brandRouter.put('/:id', strictLimiter, requireAdmin, validateId, validateBrand, handleValidationErrors, brandCRUD.updateBrand);
brandRouter.delete('/:id', strictLimiter, requireAdmin, validateId, handleValidationErrors, brandCRUD.deleteBrand);

// Brand photo routes
brandRouter.get('/:brandId/photos', brandPhotoCRUD.getPhotoByBrandId);
brandRouter.post('/:brandId/photos', strictLimiter, requireAdmin, brandPhotoCRUD.createOrUpdatePhoto);
brandRouter.delete('/:brandId/photos', strictLimiter, requireAdmin, brandPhotoCRUD.deletePhoto);

// ===== CATEGORY ROUTES =====
categoryRouter.get('/', categoryCRUD.getAllCategories);
categoryRouter.get('/:id', validateId, handleValidationErrors, categoryCRUD.getCategoryById);
categoryRouter.post('/', strictLimiter, requireAdmin, validateCategory, handleValidationErrors, categoryCRUD.createCategory);
categoryRouter.put('/:id', strictLimiter, requireAdmin, validateId, validateCategory, handleValidationErrors, categoryCRUD.updateCategory);
categoryRouter.delete('/:id', strictLimiter, requireAdmin, validateId, handleValidationErrors, categoryCRUD.deleteCategory);

// Category photo routes
categoryRouter.get('/:categoryId/photos', categoryPhotoCRUD.getPhotoByCategoryId);
categoryRouter.post('/:categoryId/photos', strictLimiter, requireAdmin, categoryPhotoCRUD.createOrUpdatePhoto);
categoryRouter.delete('/:categoryId/photos', strictLimiter, requireAdmin, categoryPhotoCRUD.deletePhoto);

// ===== PRODUCT ROUTES =====
productRouter.get('/', productCRUD.getAllProducts);
productRouter.get('/:id', productCRUD.getProductById);
productRouter.post('/', strictLimiter, requireAdmin, productCRUD.createProduct);
productRouter.put('/:id', strictLimiter, requireAdmin, productCRUD.updateProduct);
productRouter.delete('/:id', strictLimiter, requireAdmin, productCRUD.deleteProduct);

// Product variant routes
productRouter.get('/:productId/variants', variantCRUD.getVariantsByProductId);
productRouter.post('/:productId/variants', strictLimiter, requireAdmin, variantCRUD.createVariant);
productRouter.put('/variants/:id', strictLimiter, requireAdmin, variantCRUD.updateVariant);
productRouter.delete('/variants/:id', strictLimiter, requireAdmin, variantCRUD.deleteVariant);

// Product photo routes
productRouter.get('/:productId/photos', photoCRUD.getPhotosByProductId);
productRouter.post('/:productId/photos', strictLimiter, requireAdmin, photoCRUD.createPhoto);
productRouter.delete('/photos/:id', strictLimiter, requireAdmin, photoCRUD.deletePhoto);

// Product rating routes
productRouter.get('/:productId/ratings', ratingCRUD.getRatingsByProductId);
productRouter.post('/:productId/ratings', ratingLimiter, validateProductId, validateRating, handleValidationErrors, ratingCRUD.createRating);

// ===== WEB BANNER ROUTES =====
const bannerRouter = express.Router();
bannerRouter.get('/', bannerCRUD.getAllBanners);
bannerRouter.get('/active', bannerCRUD.getActiveBanners);
bannerRouter.get('/:id', validateId, handleValidationErrors, bannerCRUD.getBannerById);
bannerRouter.post('/', strictLimiter, requireAdmin, validateBanner, handleValidationErrors, bannerCRUD.createBanner);
bannerRouter.put('/:id', strictLimiter, requireAdmin, validateId, validateBanner, handleValidationErrors, bannerCRUD.updateBanner);
bannerRouter.delete('/:id', strictLimiter, requireAdmin, validateId, handleValidationErrors, bannerCRUD.deleteBanner);

// ===== IMAGE UPLOAD ROUTES =====

// Generic image upload handler
const handleImageUpload = (uploadType) => (req, res) => {
  try {
    const imageInfo = req.processedImage;
    if (!imageInfo) {
      return res.status(400).json({
        success: false,
        error: 'No image was processed'
      });
    }

    res.json({
      success: true,
      data: {
        filename: imageInfo.filename,
        url: `/uploads/${imageInfo.filename}`,
        size: imageInfo.size,
        dimensions: imageInfo.dimensions,
        uploadType
      },
      message: `${uploadType} image uploaded successfully`
    });
  } catch (error) {
    console.error(`${uploadType} image upload error:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to process ${uploadType} image upload`
    });
  }
};

// Generic multiple images upload handler
const handleMultipleImageUpload = (uploadType) => (req, res) => {
  try {
    const imagesInfo = req.processedImages;
    if (!imagesInfo || imagesInfo.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No images were processed'
      });
    }

    const uploadedImages = imagesInfo.map(img => ({
      filename: img.filename,
      url: `/uploads/${img.filename}`,
      size: img.size,
      dimensions: img.dimensions
    }));

    res.json({
      success: true,
      data: uploadedImages,
      message: `${uploadedImages.length} ${uploadType} images uploaded successfully`
    });
  } catch (error) {
    console.error(`${uploadType} images upload error:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to process ${uploadType} image uploads`
    });
  }
};

// Upload routes
uploadRouter.post('/product-image', strictLimiter, requireAdmin, uploadSingle('image'), handleImageUpload('Product'));
uploadRouter.post('/product-images', strictLimiter, requireAdmin, uploadMultiple('images', 5), handleMultipleImageUpload('Product'));
uploadRouter.post('/brand-image', strictLimiter, requireAdmin, uploadSingle('image'), handleImageUpload('Brand'));
uploadRouter.post('/category-image', strictLimiter, requireAdmin, uploadSingle('image'), handleImageUpload('Category'));
uploadRouter.post('/banner-image', strictLimiter, requireAdmin, uploadSingle('image'), handleImageUpload('Banner'));

// ===== MOUNT ROUTES =====
app.use('/api/admin', adminRouter);
app.use('/api/brands', brandRouter);
app.use('/api/categories', categoryRouter);
app.use('/api/products', productRouter);
app.use('/api/banners', bannerRouter);
app.use('/api/upload', uploadRouter);

// Additional cleanup routes that weren't in routers
app.delete('/api/brand-photos/:id', strictLimiter, requireAdmin, brandPhotoCRUD.deletePhotoById);
app.delete('/api/category-photos/:id', strictLimiter, requireAdmin, categoryPhotoCRUD.deletePhotoById);

// ===== ERROR HANDLING =====

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    path: req.path,
    method: req.method
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  
  // Handle specific error types
  if (error.type === 'entity.too.large') {
    return res.status(413).json({
      success: false,
      error: 'Request entity too large'
    });
  }

  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      error: 'Validation error',
      details: error.message
    });
  }

  // Default error response
  res.status(error.status || 500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' ? 'Internal server error' : error.message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  });
});

// ===== GRACEFUL SHUTDOWN =====
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 GG Catalog API Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔍 Database test: http://localhost:${PORT}/api/test-db`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
});

// Handle server errors
server.on('error', (error) => {
  console.error('Server error:', error);
});

module.exports = app;