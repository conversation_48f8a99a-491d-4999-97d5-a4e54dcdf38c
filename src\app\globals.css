@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Admin Dashboard Color Scheme */
  --admin-white: #FFFFFF;
  --admin-black: #000000;
  --admin-gold: #E6B120;
  --admin-light-gold: #FFCD29;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Admin Dashboard Custom Styles */
.admin-bg-primary { background-color: var(--admin-white); }
.admin-bg-secondary { background-color: var(--admin-black); }
.admin-bg-accent { background-color: var(--admin-gold); }
.admin-bg-accent-light { background-color: var(--admin-light-gold); }

.admin-text-primary { color: var(--admin-black); }
.admin-text-secondary { color: var(--admin-white); }
.admin-text-accent { color: var(--admin-gold); }
.admin-text-accent-light { color: var(--admin-light-gold); }

.admin-border-accent { border-color: var(--admin-gold); }
.admin-border-accent-light { border-color: var(--admin-light-gold); }

/* Form styles */
.admin-input {
  @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent;
}

.admin-button-primary {
  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.admin-button-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.admin-card {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}
