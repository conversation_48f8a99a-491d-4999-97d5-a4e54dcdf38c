'use client';

import React, { useEffect, useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { AdminRouteGuard } from '@/components/admin/AuthGuard';
import { useBannerPresenter } from '@/hooks/useBannerPresenter';
import { Banner, BannerForm } from '@/types/admin';
import {
  FormField,
  Input,
  Textarea,
  Button,
  Modal,
  Table,
  Alert,
  Checkbox,
} from '@/components/admin/FormComponents';
import ImageUpload from '@/components/admin/ImageUpload';

const BannersPage: React.FC = () => {
  const presenter = useBannerPresenter();
  const [showModal, setShowModal] = useState(false);
  const [editingBanner, setEditingBanner] = useState<Banner | null>(null);
  const [formData, setFormData] = useState<BannerForm>({
    title: '',
    description: '',
    image_url: '',
    link_url: '',
    is_active: true,
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    presenter.fetchBanners();
  }, []);

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      image_url: '',
      link_url: '',
      is_active: true,
    });
    setFormErrors({});
    setEditingBanner(null);
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (banner: Banner) => {
    setFormData({
      title: banner.title || '',
      description: banner.description || '',
      image_url: banner.image_url || banner.banner_image_url || '',
      link_url: banner.link_url || banner.redirect_url || '',
      is_active: banner.is_active ?? banner.active ?? true,
    });
    setEditingBanner(banner);
    setFormErrors({});
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (formData.title && formData.title.length > 255) {
      errors.title = 'Title must be less than 255 characters';
    }

    if (formData.link_url && !isValidUrl(formData.link_url)) {
      errors.link_url = 'Please enter a valid URL';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = editingBanner
      ? await presenter.updateBanner(editingBanner.id, formData)
      : await presenter.createBanner(formData);

    if (success) {
      closeModal();
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this banner? This action cannot be undone.')) {
      await presenter.deleteBanner(id);
    }
  };

  const handleImageUpload = async (files: File[]): Promise<string[]> => {
    if (files.length > 0) {
      const result = await presenter.uploadBannerImage(files[0]);
      if (result) {
        setFormData(prev => ({ ...prev, image_url: result }));
        return [result];
      }
    }
    return [];
  };

  const handleImageRemove = () => {
    setFormData(prev => ({ ...prev, image_url: '' }));
  };

  const getBannerImageUrl = (banner: Banner): string => {
    return banner.image_url || banner.banner_image_url || '';
  };

  const getBannerLinkUrl = (banner: Banner): string => {
    return banner.link_url || banner.redirect_url || '';
  };

  const getBannerStatus = (banner: Banner): boolean => {
    return banner.is_active ?? banner.active ?? false;
  };

  return (
    <AdminRouteGuard>
      <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold admin-text-primary">Banner Management</h1>
          <Button onClick={openCreateModal}>
            Add Banner
          </Button>
        </div>

        {/* Error Alert */}
        {presenter.error && (
          <Alert
            type="error"
            message={presenter.error}
            onClose={() => presenter.setError(null)}
          />
        )}

        {/* Banners Table */}
        <div className="admin-card">
          {presenter.isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-600"></div>
            </div>
          ) : (
            <Table headers={['Image', 'Title', 'Link URL', 'Status', 'Created', 'Actions']}>
              {presenter.banners.map((banner) => (
                <tr key={banner.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-16 w-32 rounded-lg overflow-hidden bg-gray-100">
                      {getBannerImageUrl(banner) ? (
                        <img
                          src={getBannerImageUrl(banner)}
                          alt={banner.title || 'Banner'}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                          <svg className="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium admin-text-primary">
                      {banner.title || 'Untitled Banner'}
                    </div>
                    <div className="text-sm text-gray-500 max-w-xs truncate">
                      {banner.description || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 max-w-xs truncate">
                      {getBannerLinkUrl(banner) ? (
                        <a
                          href={getBannerLinkUrl(banner)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="admin-text-accent hover:admin-text-accent-light"
                        >
                          {getBannerLinkUrl(banner)}
                        </a>
                      ) : (
                        '-'
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      getBannerStatus(banner)
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {getBannerStatus(banner) ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(banner.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => openEditModal(banner)}
                    >
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => handleDelete(banner.id)}
                    >
                      Delete
                    </Button>
                  </td>
                </tr>
              ))}
            </Table>
          )}

          {!presenter.isLoading && presenter.banners.length === 0 && (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No banners</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new banner.</p>
              <div className="mt-6">
                <Button onClick={openCreateModal}>
                  Add Banner
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Create/Edit Modal */}
        <Modal
          isOpen={showModal}
          onClose={closeModal}
          title={editingBanner ? 'Edit Banner' : 'Create Banner'}
          maxWidth="xl"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Banner Title" error={formErrors.title}>
                <Input
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Enter banner title"
                  error={!!formErrors.title}
                />
              </FormField>

              <FormField label="Link URL" error={formErrors.link_url}>
                <Input
                  type="url"
                  value={formData.link_url}
                  onChange={(e) => setFormData({ ...formData, link_url: e.target.value })}
                  placeholder="https://example.com"
                  error={!!formErrors.link_url}
                />
              </FormField>
            </div>

            <FormField label="Description" className="md:col-span-2">
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter banner description (optional)"
                rows={3}
              />
            </FormField>

            {/* Banner Image Upload - spans 3 columns as requested */}
            <FormField label="Banner Image" className="md:col-span-3">
              <ImageUpload
                onUpload={handleImageUpload}
                multiple={false}
                maxFiles={1}
                aspectRatio="13:5"
                currentImages={formData.image_url ? [formData.image_url] : []}
                onRemove={handleImageRemove}
              />
            </FormField>

            <div className="flex items-center justify-between">
              <Checkbox
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                label="Active Banner"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button type="button" variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
              <Button type="submit" loading={presenter.isLoading}>
                {editingBanner ? 'Update Banner' : 'Create Banner'}
              </Button>
            </div>
          </form>
        </Modal>
      </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
};

export default BannersPage;
