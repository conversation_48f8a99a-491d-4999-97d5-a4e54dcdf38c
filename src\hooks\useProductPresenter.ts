'use client';

import { useState, useCallback, useEffect } from 'react';
import { Product, ProductForm, VariantForm, ProductPresenter } from '@/types/admin';
import { productApi, brandApi, categoryApi } from '@/lib/adminApi';
import { useBasePresenter } from './useBasePresenter';
import { useWebhooks, triggerWebhookEvent } from '@/lib/webhooks';

export const useProductPresenter = (): ProductPresenter & {
  brands: any[];
  categories: any[];
  fetchBrands: () => Promise<void>;
  fetchCategories: () => Promise<void>;
} => {
  const basePresenter = useBasePresenter();
  const [products, setProducts] = useState<Product[]>([]);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [brands, setBrands] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);

  // Set up webhook listeners for live updates
  useWebhooks([
    {
      type: 'product_created',
      callback: (event) => {
        console.log('Product created:', event.data);
        fetchProducts(); // Refresh products list
      }
    },
    {
      type: 'product_updated',
      callback: (event) => {
        console.log('Product updated:', event.data);
        fetchProducts(); // Refresh products list
      }
    },
    {
      type: 'product_deleted',
      callback: (event) => {
        console.log('Product deleted:', event.data);
        setProducts(prev => prev.filter(p => p.id !== event.data.id));
      }
    }
  ]);

  const fetchProducts = useCallback(async () => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.getAll(1, 100); // Get more products for admin
      if (response.success && response.data) {
        setProducts(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch products');
    });
    return result !== null;
  }, [basePresenter]);

  const fetchBrands = useCallback(async () => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await brandApi.getAll();
      if (response.success && response.data) {
        setBrands(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch brands');
    });
    return result !== null;
  }, [basePresenter]);

  const fetchCategories = useCallback(async () => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await categoryApi.getAll();
      if (response.success && response.data) {
        setCategories(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch categories');
    });
    return result !== null;
  }, [basePresenter]);

  const createProduct = useCallback(async (data: ProductForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.create(data);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('product_created', { id: response.id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to create product');
    });
    return result === true;
  }, [basePresenter, fetchProducts]);

  const updateProduct = useCallback(async (id: number, data: ProductForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.update(id, data);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('product_updated', { id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to update product');
    });
    return result === true;
  }, [basePresenter, fetchProducts]);

  const deleteProduct = useCallback(async (id: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.delete(id);
      if (response.success) {
        setProducts(prev => prev.filter(p => p.id !== id));
        // Trigger webhook event for live updates
        triggerWebhookEvent('product_deleted', { id });
        return true;
      }
      throw new Error(response.error || 'Failed to delete product');
    });
    return result === true;
  }, [basePresenter]);

  const uploadProductImages = useCallback(async (productId: number, files: File[]): Promise<string[]> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.uploadImages(files);
      if (response.success && response.data) {
        // Add photos to the product
        const photoPromises = response.data.map(img =>
          productApi.addPhoto(productId, img.url)
        );
        await Promise.all(photoPromises);

        // Refresh the product data
        await fetchProducts();

        return response.data.map(img => img.url);
      }
      throw new Error(response.error || 'Failed to upload images');
    });
    return result || [];
  }, [basePresenter, fetchProducts]);

  const deleteProductPhoto = useCallback(async (photoId: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.deletePhoto(photoId);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        return true;
      }
      throw new Error(response.error || 'Failed to delete photo');
    });
    return result === true;
  }, [basePresenter, fetchProducts]);

  const addVariant = useCallback(async (productId: number, data: VariantForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.createVariant(productId, data);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        return true;
      }
      throw new Error(response.error || 'Failed to add variant');
    });
    return result === true;
  }, [basePresenter, fetchProducts]);

  const updateVariant = useCallback(async (id: number, data: VariantForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.updateVariant(id, data);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        return true;
      }
      throw new Error(response.error || 'Failed to update variant');
    });
    return result === true;
  }, [basePresenter, fetchProducts]);

  const deleteVariant = useCallback(async (id: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.deleteVariant(id);
      if (response.success) {
        await fetchProducts(); // Refresh the list
        return true;
      }
      throw new Error(response.error || 'Failed to delete variant');
    });
    return result === true;
  }, [basePresenter, fetchProducts]);

  const getProductById = useCallback(async (id: number): Promise<Product | null> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await productApi.getById(id);
      if (response.success && response.data) {
        setCurrentProduct(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch product');
    });
    return result;
  }, [basePresenter]);

  return {
    ...basePresenter,
    products,
    currentProduct,
    brands,
    categories,
    fetchProducts,
    fetchBrands,
    fetchCategories,
    createProduct,
    updateProduct,
    deleteProduct,
    uploadProductImages,
    deleteProductPhoto,
    addVariant,
    updateVariant,
    deleteVariant,
    getProductById,
    setCurrentProduct,
  } as ProductPresenter & {
    brands: any[];
    categories: any[];
    fetchBrands: () => Promise<void>;
    fetchCategories: () => Promise<void>;
    deleteProductPhoto: (photoId: number) => Promise<boolean>;
    getProductById: (id: number) => Promise<Product | null>;
    setCurrentProduct: (product: Product | null) => void;
  };
};
