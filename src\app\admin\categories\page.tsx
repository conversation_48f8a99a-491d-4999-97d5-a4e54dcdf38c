'use client';

import React, { useEffect, useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { AdminRouteGuard } from '@/components/admin/AuthGuard';
import { useCategoryPresenter } from '@/hooks/useCategoryPresenter';
import { Category, CategoryForm } from '@/types/admin';
import {
  FormField,
  Input,
  Textarea,
  Button,
  Modal,
  Table,
  Alert,
} from '@/components/admin/FormComponents';
import ImageUpload from '@/components/admin/ImageUpload';

const CategoriesPage: React.FC = () => {
  const presenter = useCategoryPresenter();
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<CategoryForm>({
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    presenter.fetchCategories();
  }, []);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
    });
    setFormErrors({});
    setEditingCategory(null);
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (category: Category) => {
    setFormData({
      name: category.name,
      description: category.description || '',
    });
    setEditingCategory(category);
    setFormErrors({});
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Category name is required';
    }

    if (formData.name.length > 100) {
      errors.name = 'Category name must be less than 100 characters';
    }

    if (formData.description && formData.description.length > 1000) {
      errors.description = 'Description must be less than 1000 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = editingCategory
      ? await presenter.updateCategory(editingCategory.id, formData)
      : await presenter.createCategory(formData);

    if (success) {
      closeModal();
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      await presenter.deleteCategory(id);
    }
  };

  const handleImageUpload = async (categoryId: number, files: File[]): Promise<string[]> => {
    if (files.length > 0) {
      const result = await presenter.uploadCategoryImage(categoryId, files[0]);
      return result ? [result] : [];
    }
    return [];
  };

  const handleImageRemove = async (category: Category) => {
    if (window.confirm('Are you sure you want to remove this category image?')) {
      await (presenter as any).deleteCategoryImage(category.id);
    }
  };

  return (
    <AdminRouteGuard>
      <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold admin-text-primary">Category Management</h1>
          <Button onClick={openCreateModal}>
            Add Category
          </Button>
        </div>

        {/* Error Alert */}
        {presenter.error && (
          <Alert
            type="error"
            message={presenter.error}
            onClose={() => presenter.setError(null)}
          />
        )}

        {/* Categories Table */}
        <div className="admin-card">
          {presenter.isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-600"></div>
            </div>
          ) : (
            <Table headers={['Image', 'Name', 'Description', 'Created', 'Actions']}>
              {presenter.categories.map((category) => (
                <tr key={category.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-12 w-12 rounded-lg overflow-hidden">
                      {category.photo?.photo_url ? (
                        <img
                          src={category.photo.photo_url}
                          alt={category.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                          <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium admin-text-primary">{category.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 max-w-xs truncate">
                      {category.description || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(category.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => openEditModal(category)}
                    >
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => handleDelete(category.id)}
                    >
                      Delete
                    </Button>
                  </td>
                </tr>
              ))}
            </Table>
          )}

          {!presenter.isLoading && presenter.categories.length === 0 && (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No categories</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new category.</p>
              <div className="mt-6">
                <Button onClick={openCreateModal}>
                  Add Category
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Create/Edit Modal */}
        <Modal
          isOpen={showModal}
          onClose={closeModal}
          title={editingCategory ? 'Edit Category' : 'Create Category'}
          maxWidth="lg"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Category Name" required error={formErrors.name}>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter category name"
                  error={!!formErrors.name}
                />
              </FormField>

              {/* Image Upload for existing categories */}
              {editingCategory && (
                <FormField label="Category Image">
                  <ImageUpload
                    onUpload={(files) => handleImageUpload(editingCategory.id, files)}
                    multiple={false}
                    maxFiles={1}
                    aspectRatio="1:1"
                    currentImages={editingCategory.photo?.photo_url ? [editingCategory.photo.photo_url] : []}
                    onRemove={() => handleImageRemove(editingCategory)}
                  />
                </FormField>
              )}
            </div>

            <FormField label="Description" error={formErrors.description} className="md:col-span-2">
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter category description (optional)"
                rows={4}
                error={!!formErrors.description}
              />
            </FormField>

            <div className="flex justify-end space-x-3">
              <Button type="button" variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
              <Button type="submit" loading={presenter.isLoading}>
                {editingCategory ? 'Update Category' : 'Create Category'}
              </Button>
            </div>
          </form>
        </Modal>
      </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
};

export default CategoriesPage;
