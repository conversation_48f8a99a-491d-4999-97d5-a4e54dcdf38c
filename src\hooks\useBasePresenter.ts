'use client';

import { useState, useCallback } from 'react';
import { BasePresenter } from '@/types/admin';

export const useBasePresenter = (): BasePresenter => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleError = useCallback((error: any) => {
    console.error('Presenter error:', error);
    if (error.response?.data?.error) {
      setError(error.response.data.error);
    } else if (error.message) {
      setError(error.message);
    } else {
      setError('An unexpected error occurred');
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const withLoading = useCallback(async <T>(
    operation: () => Promise<T>
  ): Promise<T | null> => {
    try {
      setIsLoading(true);
      clearError();
      return await operation();
    } catch (error) {
      handleError(error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [handleError, clearError]);

  return {
    isLoading,
    error,
    setError: clearError,
    withLoading,
    handleError,
  } as BasePresenter & {
    withLoading: <T>(operation: () => Promise<T>) => Promise<T | null>;
    handleError: (error: any) => void;
  };
};
