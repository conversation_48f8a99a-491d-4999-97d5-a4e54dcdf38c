'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to catalog page for regular customers
    // Admin users can access /login directly
    router.push('/katalog');
  }, [router]);

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-white">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E6B120] mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading GG Catalog Store...</p>
      </div>
    </div>
  );
}