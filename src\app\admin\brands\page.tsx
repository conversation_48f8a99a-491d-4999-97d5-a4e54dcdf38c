'use client';

import React, { useEffect, useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { AdminRouteGuard } from '@/components/admin/AuthGuard';
import { useBrandPresenter } from '@/hooks/useBrandPresenter';
import { Brand, BrandForm } from '@/types/admin';
import {
  FormField,
  Input,
  Textarea,
  Button,
  Modal,
  Table,
  Alert,
} from '@/components/admin/FormComponents';
import ImageUpload from '@/components/admin/ImageUpload';

const BrandsPage: React.FC = () => {
  const presenter = useBrandPresenter();
  const [showModal, setShowModal] = useState(false);
  const [editingBrand, setEditingBrand] = useState<Brand | null>(null);
  const [formData, setFormData] = useState<BrandForm>({
    name: '',
    description: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    presenter.fetchBrands();
  }, []);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
    });
    setFormErrors({});
    setEditingBrand(null);
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (brand: Brand) => {
    setFormData({
      name: brand.name,
      description: brand.description || '',
    });
    setEditingBrand(brand);
    setFormErrors({});
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Brand name is required';
    }

    if (formData.name.length > 100) {
      errors.name = 'Brand name must be less than 100 characters';
    }

    if (formData.description && formData.description.length > 1000) {
      errors.description = 'Description must be less than 1000 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = editingBrand
      ? await presenter.updateBrand(editingBrand.id, formData)
      : await presenter.createBrand(formData);

    if (success) {
      closeModal();
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this brand? This action cannot be undone.')) {
      await presenter.deleteBrand(id);
    }
  };

  const handleImageUpload = async (brandId: number, files: File[]): Promise<string[]> => {
    if (files.length > 0) {
      const result = await presenter.uploadBrandImage(brandId, files[0]);
      return result ? [result] : [];
    }
    return [];
  };

  const handleImageRemove = async (brand: Brand) => {
    if (window.confirm('Are you sure you want to remove this brand image?')) {
      await (presenter as any).deleteBrandImage(brand.id);
    }
  };

  return (
    <AdminRouteGuard>
      <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold admin-text-primary">Brand Management</h1>
          <Button onClick={openCreateModal}>
            Add Brand
          </Button>
        </div>

        {/* Error Alert */}
        {presenter.error && (
          <Alert
            type="error"
            message={presenter.error}
            onClose={() => presenter.setError(null)}
          />
        )}

        {/* Brands Table */}
        <div className="admin-card">
          {presenter.isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-600"></div>
            </div>
          ) : (
            <Table headers={['Image', 'Name', 'Description', 'Created', 'Actions']}>
              {presenter.brands.map((brand) => (
                <tr key={brand.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-12 w-12 rounded-lg overflow-hidden">
                      {brand.photo?.photo_url ? (
                        <img
                          src={brand.photo.photo_url}
                          alt={brand.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                          <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium admin-text-primary">{brand.name}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 max-w-xs truncate">
                      {brand.description || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(brand.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => openEditModal(brand)}
                    >
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => handleDelete(brand.id)}
                    >
                      Delete
                    </Button>
                  </td>
                </tr>
              ))}
            </Table>
          )}

          {!presenter.isLoading && presenter.brands.length === 0 && (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No brands</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new brand.</p>
              <div className="mt-6">
                <Button onClick={openCreateModal}>
                  Add Brand
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Create/Edit Modal */}
        <Modal
          isOpen={showModal}
          onClose={closeModal}
          title={editingBrand ? 'Edit Brand' : 'Create Brand'}
          maxWidth="lg"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Brand Name" required error={formErrors.name}>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter brand name"
                  error={!!formErrors.name}
                />
              </FormField>

              {/* Image Upload for existing brands */}
              {editingBrand && (
                <FormField label="Brand Image">
                  <ImageUpload
                    onUpload={(files) => handleImageUpload(editingBrand.id, files)}
                    multiple={false}
                    maxFiles={1}
                    aspectRatio="1:1"
                    currentImages={editingBrand.photo?.photo_url ? [editingBrand.photo.photo_url] : []}
                    onRemove={() => handleImageRemove(editingBrand)}
                  />
                </FormField>
              )}
            </div>

            <FormField label="Description" error={formErrors.description} className="md:col-span-2">
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter brand description (optional)"
                rows={4}
                error={!!formErrors.description}
              />
            </FormField>

            <div className="flex justify-end space-x-3">
              <Button type="button" variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
              <Button type="submit" loading={presenter.isLoading}>
                {editingBrand ? 'Update Brand' : 'Create Brand'}
              </Button>
            </div>
          </form>
        </Modal>
      </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
};

export default BrandsPage;
