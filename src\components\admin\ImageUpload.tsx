'use client';

import React, { useState, useRef, useCallback } from 'react';

interface ImageUploadProps {
  onUpload: (files: File[]) => Promise<string[]>;
  multiple?: boolean;
  maxFiles?: number;
  aspectRatio?: '1:1' | '13:5' | 'auto';
  currentImages?: string[];
  onRemove?: (imageUrl: string) => void;
  className?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onUpload,
  multiple = false,
  maxFiles = 1,
  aspectRatio = '1:1',
  currentImages = [],
  onRemove,
  className = '',
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFiles = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(file => file.type.startsWith('image/'));
    
    if (validFiles.length === 0) {
      alert('Please select valid image files.');
      return;
    }

    const totalImages = currentImages.length + validFiles.length;
    if (totalImages > maxFiles) {
      alert(`Maximum ${maxFiles} images allowed.`);
      return;
    }

    // Create preview URLs
    const previews = validFiles.map(file => URL.createObjectURL(file));
    setPreviewImages(prev => [...prev, ...previews]);

    try {
      setIsUploading(true);
      await onUpload(validFiles);
      setPreviewImages([]);
    } catch (error) {
      console.error('Upload failed:', error);
      // Clean up preview URLs on error
      previews.forEach(url => URL.revokeObjectURL(url));
      setPreviewImages(prev => prev.filter(url => !previews.includes(url)));
    } finally {
      setIsUploading(false);
    }
  }, [currentImages.length, maxFiles, onUpload]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveImage = (imageUrl: string) => {
    if (onRemove) {
      onRemove(imageUrl);
    }
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case '1:1':
        return 'aspect-square';
      case '13:5':
        return 'aspect-[13/5]';
      default:
        return 'aspect-auto';
    }
  };

  const allImages = [...currentImages, ...previewImages];
  const canUploadMore = allImages.length < maxFiles;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Current and Preview Images */}
      {allImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {allImages.map((imageUrl, index) => (
            <div key={index} className="relative group">
              <div className={`${getAspectRatioClass()} rounded-lg overflow-hidden border border-gray-200`}>
                <img
                  src={imageUrl}
                  alt={`Upload ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                {isUploading && previewImages.includes(imageUrl) && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                )}
              </div>
              {onRemove && currentImages.includes(imageUrl) && (
                <button
                  type="button"
                  onClick={() => handleRemoveImage(imageUrl)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Upload Area */}
      {canUploadMore && (
        <div
          className={`relative border-2 border-dashed rounded-lg p-6 transition-colors duration-200 ${
            dragActive
              ? 'border-yellow-500 bg-yellow-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple={multiple}
            accept="image/*"
            onChange={handleChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={isUploading}
          />
          
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div className="mt-4">
              <p className="text-sm text-gray-600">
                <button
                  type="button"
                  onClick={handleClick}
                  className="font-medium admin-text-accent hover:admin-text-accent-light"
                  disabled={isUploading}
                >
                  Click to upload
                </button>
                {' '}or drag and drop
              </p>
              <p className="text-xs text-gray-500 mt-1">
                PNG, JPG, GIF up to 10MB
                {multiple && ` (max ${maxFiles} files)`}
              </p>
              {aspectRatio !== 'auto' && (
                <p className="text-xs text-gray-500 mt-1">
                  Images will be automatically cropped to {aspectRatio} ratio and converted to WebP
                </p>
              )}
            </div>
          </div>

          {isUploading && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
                <p className="text-sm text-gray-600 mt-2">Uploading...</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Upload Info */}
      <div className="text-sm text-gray-500">
        {multiple ? (
          <p>
            {allImages.length} of {maxFiles} images uploaded
          </p>
        ) : (
          currentImages.length > 0 && <p>Image uploaded successfully</p>
        )}
      </div>
    </div>
  );
};

export default ImageUpload;
