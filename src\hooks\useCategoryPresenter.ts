'use client';

import { useState, useCallback } from 'react';
import { Category, CategoryForm, CategoryPresenter } from '@/types/admin';
import { categoryApi } from '@/lib/adminApi';
import { useBasePresenter } from './useBasePresenter';
import { useWebhooks, triggerWebhookEvent } from '@/lib/webhooks';

export const useCategoryPresenter = (): CategoryPresenter => {
  const basePresenter = useBasePresenter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null);

  // Set up webhook listeners for live updates
  useWebhooks([
    {
      type: 'category_created',
      callback: (event) => {
        console.log('Category created:', event.data);
        fetchCategories(); // Refresh categories list
      }
    },
    {
      type: 'category_updated',
      callback: (event) => {
        console.log('Category updated:', event.data);
        fetchCategories(); // Refresh categories list
      }
    },
    {
      type: 'category_deleted',
      callback: (event) => {
        console.log('Category deleted:', event.data);
        setCategories(prev => prev.filter(c => c.id !== event.data.id));
      }
    }
  ]);

  const fetchCategories = useCallback(async (): Promise<void> => {
    await (basePresenter as any).withLoading(async () => {
      const response = await categoryApi.getAll();
      if (response.success && response.data) {
        setCategories(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch categories');
    });
  }, [basePresenter]);

  const createCategory = useCallback(async (data: CategoryForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await categoryApi.create(data);
      if (response.success) {
        await fetchCategories(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('category_created', { id: response.id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to create category');
    });
    return result === true;
  }, [basePresenter, fetchCategories]);

  const updateCategory = useCallback(async (id: number, data: CategoryForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await categoryApi.update(id, data);
      if (response.success) {
        await fetchCategories(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('category_updated', { id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to update category');
    });
    return result === true;
  }, [basePresenter, fetchCategories]);

  const deleteCategory = useCallback(async (id: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await categoryApi.delete(id);
      if (response.success) {
        setCategories(prev => prev.filter(c => c.id !== id));
        // Trigger webhook event for live updates
        triggerWebhookEvent('category_deleted', { id });
        return true;
      }
      throw new Error(response.error || 'Failed to delete category');
    });
    return result === true;
  }, [basePresenter]);

  const uploadCategoryImage = useCallback(async (categoryId: number, file: File): Promise<string | null> => {
    const result = await (basePresenter as any).withLoading(async () => {
      // Upload the image
      const uploadResponse = await categoryApi.uploadImage(file);
      if (uploadResponse.success && uploadResponse.data) {
        // Associate the image with the category
        const photoResponse = await categoryApi.createOrUpdatePhoto(categoryId, uploadResponse.data.url);
        if (photoResponse.success) {
          // Refresh the categories list to show the new image
          await fetchCategories();
          return uploadResponse.data.url;
        }
        throw new Error(photoResponse.error || 'Failed to associate image with category');
      }
      throw new Error(uploadResponse.error || 'Failed to upload image');
    });
    return result;
  }, [basePresenter, fetchCategories]);

  const deleteCategoryImage = useCallback(async (categoryId: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await categoryApi.deletePhoto(categoryId);
      if (response.success) {
        await fetchCategories(); // Refresh the list
        return true;
      }
      throw new Error(response.error || 'Failed to delete category image');
    });
    return result === true;
  }, [basePresenter, fetchCategories]);

  const getCategoryById = useCallback(async (id: number): Promise<Category | null> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await categoryApi.getById(id);
      if (response.success && response.data) {
        setCurrentCategory(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch category');
    });
    return result;
  }, [basePresenter]);

  return {
    ...basePresenter,
    categories,
    currentCategory,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    uploadCategoryImage,
    getCategoryById,
    setCurrentCategory,
    deleteCategoryImage,
  } as CategoryPresenter & {
    getCategoryById: (id: number) => Promise<Category | null>;
    setCurrentCategory: (category: Category | null) => void;
    deleteCategoryImage: (categoryId: number) => Promise<boolean>;
  };
};
