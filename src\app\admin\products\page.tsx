'use client';

import React, { useEffect, useState } from 'react';
import { withAdminAuth } from '@/contexts/AdminAuthContext';
import AdminLayout from '@/components/admin/AdminLayout';
import { useProductPresenter } from '@/hooks/useProductPresenter';
import { Product, ProductForm } from '@/types/admin';
import {
  FormField,
  Input,
  Textarea,
  Select,
  Button,
  Modal,
  Table,
  Alert,
} from '@/components/admin/FormComponents';
import ImageUpload from '@/components/admin/ImageUpload';

const ProductsPage: React.FC = () => {
  const presenter = useProductPresenter();
  const [showModal, setShowModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState<ProductForm>({
    name: '',
    description: '',
    price: 0,
    brand_id: undefined,
    category_id: undefined,
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    presenter.fetchProducts();
    presenter.fetchBrands();
    presenter.fetchCategories();
  }, []);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      brand_id: undefined,
      category_id: undefined,
    });
    setFormErrors({});
    setEditingProduct(null);
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (product: Product) => {
    setFormData({
      name: product.name,
      description: product.description || '',
      price: product.price,
      brand_id: product.brand_id || undefined,
      category_id: product.category_id || undefined,
    });
    setEditingProduct(product);
    setFormErrors({});
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = 'Product name is required';
    }

    if (formData.price < 0) {
      errors.price = 'Price must be a positive number';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = editingProduct
      ? await presenter.updateProduct(editingProduct.id, formData)
      : await presenter.createProduct(formData);

    if (success) {
      closeModal();
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      await presenter.deleteProduct(id);
    }
  };

  const handleImageUpload = async (productId: number, files: File[]): Promise<string[]> => {
    return await presenter.uploadProductImages(productId, files);
  };

  const handleImageRemove = async (product: Product, imageUrl: string) => {
    const photo = product.photos?.find(p => p.photo_url === imageUrl);
    if (photo) {
      await presenter.deleteProductPhoto(photo.id);
    }
  };

  const brandOptions = presenter.brands.map(brand => ({
    value: brand.id,
    label: brand.name,
  }));

  const categoryOptions = presenter.categories.map(category => ({
    value: category.id,
    label: category.name,
  }));

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold admin-text-primary">Product Management</h1>
          <Button onClick={openCreateModal}>
            Add Product
          </Button>
        </div>

        {/* Error Alert */}
        {presenter.error && (
          <Alert
            type="error"
            message={presenter.error}
            onClose={() => presenter.setError(null)}
          />
        )}

        {/* Products Table */}
        <div className="admin-card">
          {presenter.isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-600"></div>
            </div>
          ) : (
            <Table headers={['Image', 'Name', 'Brand', 'Category', 'Price', 'Actions']}>
              {presenter.products.map((product) => (
                <tr key={product.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-10 w-10 rounded-lg overflow-hidden">
                      {product.photos && product.photos.length > 0 ? (
                        <img
                          src={product.photos[0].photo_url}
                          alt={product.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                          <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium admin-text-primary">{product.name}</div>
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {product.description}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.brand_name || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.category_name || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${product.price.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => openEditModal(product)}
                    >
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => handleDelete(product.id)}
                    >
                      Delete
                    </Button>
                  </td>
                </tr>
              ))}
            </Table>
          )}

          {!presenter.isLoading && presenter.products.length === 0 && (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new product.</p>
              <div className="mt-6">
                <Button onClick={openCreateModal}>
                  Add Product
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Create/Edit Modal */}
        <Modal
          isOpen={showModal}
          onClose={closeModal}
          title={editingProduct ? 'Edit Product' : 'Create Product'}
          maxWidth="lg"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Product Name" required error={formErrors.name}>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter product name"
                  error={!!formErrors.name}
                />
              </FormField>

              <FormField label="Price" required error={formErrors.price}>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                  placeholder="0.00"
                  error={!!formErrors.price}
                />
              </FormField>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Brand">
                <Select
                  value={formData.brand_id || ''}
                  onChange={(e) => setFormData({ ...formData, brand_id: e.target.value ? parseInt(e.target.value) : undefined })}
                  options={brandOptions}
                  placeholder="Select a brand"
                />
              </FormField>

              <FormField label="Category">
                <Select
                  value={formData.category_id || ''}
                  onChange={(e) => setFormData({ ...formData, category_id: e.target.value ? parseInt(e.target.value) : undefined })}
                  options={categoryOptions}
                  placeholder="Select a category"
                />
              </FormField>
            </div>

            <FormField label="Description" className="md:col-span-2">
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter product description"
                rows={4}
              />
            </FormField>

            {/* Image Upload for existing products */}
            {editingProduct && (
              <FormField label="Product Images" className="md:col-span-2">
                <ImageUpload
                  onUpload={(files) => handleImageUpload(editingProduct.id, files)}
                  multiple={true}
                  maxFiles={5}
                  aspectRatio="1:1"
                  currentImages={editingProduct.photos?.map(p => p.photo_url) || []}
                  onRemove={(imageUrl) => handleImageRemove(editingProduct, imageUrl)}
                />
              </FormField>
            )}

            <div className="flex justify-end space-x-3">
              <Button type="button" variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
              <Button type="submit" loading={presenter.isLoading}>
                {editingProduct ? 'Update Product' : 'Create Product'}
              </Button>
            </div>
          </form>
        </Modal>
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(ProductsPage);
