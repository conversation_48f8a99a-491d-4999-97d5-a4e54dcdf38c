import axios, { AxiosResponse } from 'axios';
import {
  Admin,
  Brand,
  Category,
  Product,
  Banner,
  ApiResponse,
  PaginatedResponse,
  LoginForm,
  AdminForm,
  BrandForm,
  CategoryForm,
  ProductForm,
  BannerForm,
  VariantForm,
  UploadResponse,
  MultipleUploadResponse,
  ProductVariant,
} from '@/types/admin';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',
  withCredentials: true, // Important for session cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login on unauthorized
      if (typeof window !== 'undefined') {
        window.location.href = '/admin/login';
      }
    }
    return Promise.reject(error);
  }
);

// Authentication API
export const authApi = {
  login: async (credentials: LoginForm): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.post('/admin/login', credentials);
    return response.data;
  },

  logout: async (): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.post('/admin/logout');
    return response.data;
  },

  checkAuth: async (): Promise<ApiResponse<Admin>> => {
    const response: AxiosResponse<ApiResponse<Admin>> = await api.get('/admin/check');
    return response.data;
  },
};

// Admin Management API
export const adminApi = {
  getAll: async (): Promise<ApiResponse<Admin[]>> => {
    const response: AxiosResponse<ApiResponse<Admin[]>> = await api.get('/admin');
    return response.data;
  },

  create: async (data: AdminForm): Promise<ApiResponse<{ id: number }>> => {
    const response: AxiosResponse<ApiResponse<{ id: number }>> = await api.post('/admin', data);
    return response.data;
  },
};

// Brand Management API
export const brandApi = {
  getAll: async (): Promise<ApiResponse<Brand[]>> => {
    const response: AxiosResponse<ApiResponse<Brand[]>> = await api.get('/brands');
    return response.data;
  },

  getById: async (id: number): Promise<ApiResponse<Brand>> => {
    const response: AxiosResponse<ApiResponse<Brand>> = await api.get(`/brands/${id}`);
    return response.data;
  },

  create: async (data: BrandForm): Promise<ApiResponse<{ id: number }>> => {
    const response: AxiosResponse<ApiResponse<{ id: number }>> = await api.post('/brands', data);
    return response.data;
  },

  update: async (id: number, data: BrandForm): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.put(`/brands/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/brands/${id}`);
    return response.data;
  },

  uploadImage: async (file: File): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('image', file);
    const response: AxiosResponse<UploadResponse> = await api.post('/upload/brand-image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  },

  createOrUpdatePhoto: async (brandId: number, photoUrl: string): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.post(`/brands/${brandId}/photos`, {
      photo_url: photoUrl,
    });
    return response.data;
  },

  deletePhoto: async (brandId: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/brands/${brandId}/photos`);
    return response.data;
  },
};

// Category Management API
export const categoryApi = {
  getAll: async (): Promise<ApiResponse<Category[]>> => {
    const response: AxiosResponse<ApiResponse<Category[]>> = await api.get('/categories');
    return response.data;
  },

  getById: async (id: number): Promise<ApiResponse<Category>> => {
    const response: AxiosResponse<ApiResponse<Category>> = await api.get(`/categories/${id}`);
    return response.data;
  },

  create: async (data: CategoryForm): Promise<ApiResponse<{ id: number }>> => {
    const response: AxiosResponse<ApiResponse<{ id: number }>> = await api.post('/categories', data);
    return response.data;
  },

  update: async (id: number, data: CategoryForm): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.put(`/categories/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/categories/${id}`);
    return response.data;
  },

  uploadImage: async (file: File): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('image', file);
    const response: AxiosResponse<UploadResponse> = await api.post('/upload/category-image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  },

  createOrUpdatePhoto: async (categoryId: number, photoUrl: string): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.post(`/categories/${categoryId}/photos`, {
      photo_url: photoUrl,
    });
    return response.data;
  },

  deletePhoto: async (categoryId: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/categories/${categoryId}/photos`);
    return response.data;
  },
};

// Product Management API
export const productApi = {
  getAll: async (page = 1, limit = 10): Promise<PaginatedResponse<Product>> => {
    const response: AxiosResponse<PaginatedResponse<Product>> = await api.get(`/products?page=${page}&limit=${limit}`);
    return response.data;
  },

  getById: async (id: number): Promise<ApiResponse<Product>> => {
    const response: AxiosResponse<ApiResponse<Product>> = await api.get(`/products/${id}`);
    return response.data;
  },

  create: async (data: ProductForm): Promise<ApiResponse<{ id: number }>> => {
    const response: AxiosResponse<ApiResponse<{ id: number }>> = await api.post('/products', data);
    return response.data;
  },

  update: async (id: number, data: ProductForm): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.put(`/products/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/products/${id}`);
    return response.data;
  },

  uploadImages: async (files: File[]): Promise<MultipleUploadResponse> => {
    const formData = new FormData();
    files.forEach((file) => formData.append('images', file));
    const response: AxiosResponse<MultipleUploadResponse> = await api.post('/upload/product-images', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  },

  addPhoto: async (productId: number, photoUrl: string): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.post(`/products/${productId}/photos`, {
      photo_url: photoUrl,
    });
    return response.data;
  },

  deletePhoto: async (photoId: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/products/photos/${photoId}`);
    return response.data;
  },

  // Variant management
  getVariants: async (productId: number): Promise<ApiResponse<ProductVariant[]>> => {
    const response: AxiosResponse<ApiResponse<ProductVariant[]>> = await api.get(`/products/${productId}/variants`);
    return response.data;
  },

  createVariant: async (productId: number, data: VariantForm): Promise<ApiResponse<{ id: number }>> => {
    const response: AxiosResponse<ApiResponse<{ id: number }>> = await api.post(`/products/${productId}/variants`, data);
    return response.data;
  },

  updateVariant: async (variantId: number, data: VariantForm): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.put(`/products/variants/${variantId}`, data);
    return response.data;
  },

  deleteVariant: async (variantId: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/products/variants/${variantId}`);
    return response.data;
  },
};

// Banner Management API
export const bannerApi = {
  getAll: async (): Promise<ApiResponse<Banner[]>> => {
    const response: AxiosResponse<ApiResponse<Banner[]>> = await api.get('/banners');
    return response.data;
  },

  getById: async (id: number): Promise<ApiResponse<Banner>> => {
    const response: AxiosResponse<ApiResponse<Banner>> = await api.get(`/banners/${id}`);
    return response.data;
  },

  create: async (data: BannerForm): Promise<ApiResponse<{ id: number }>> => {
    const response: AxiosResponse<ApiResponse<{ id: number }>> = await api.post('/banners', data);
    return response.data;
  },

  update: async (id: number, data: BannerForm): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.put(`/banners/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<ApiResponse<any>> => {
    const response: AxiosResponse<ApiResponse<any>> = await api.delete(`/banners/${id}`);
    return response.data;
  },

  uploadImage: async (file: File): Promise<UploadResponse> => {
    const formData = new FormData();
    formData.append('image', file);
    const response: AxiosResponse<UploadResponse> = await api.post('/upload/banner-image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  },
};

export default api;
