'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { withAdminAuth } from '@/contexts/AdminAuthContext';
import AdminLayout from '@/components/admin/AdminLayout';
import { productApi, brandApi, categoryApi, bannerApi, adminApi } from '@/lib/adminApi';
import { Product, Brand, Category, Banner } from '@/types/admin';

interface DashboardStats {
  totalProducts: number;
  totalBrands: number;
  totalCategories: number;
  totalBanners: number;
  totalAdmins: number;
}

const AdminDashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalBrands: 0,
    totalCategories: 0,
    totalBanners: 0,
    totalAdmins: 0,
  });
  const [recentProducts, setRecentProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch all data in parallel
        const [
          productsResponse,
          brandsResponse,
          categoriesResponse,
          bannersResponse,
          adminsResponse,
        ] = await Promise.all([
          productApi.getAll(1, 5), // Get first 5 products for recent items
          brandApi.getAll(),
          categoryApi.getAll(),
          bannerApi.getAll(),
          adminApi.getAll(),
        ]);

        // Update stats
        setStats({
          totalProducts: productsResponse.pagination?.total || 0,
          totalBrands: brandsResponse.data?.length || 0,
          totalCategories: categoriesResponse.data?.length || 0,
          totalBanners: bannersResponse.data?.length || 0,
          totalAdmins: adminsResponse.data?.length || 0,
        });

        // Set recent products
        if (productsResponse.data) {
          setRecentProducts(productsResponse.data);
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const statCards = [
    {
      name: 'Total Products',
      value: stats.totalProducts,
      href: '/admin/products',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      color: 'admin-bg-accent',
    },
    {
      name: 'Total Brands',
      value: stats.totalBrands,
      href: '/admin/brands',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
        </svg>
      ),
      color: 'bg-blue-500',
    },
    {
      name: 'Total Categories',
      value: stats.totalCategories,
      href: '/admin/categories',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      color: 'bg-green-500',
    },
    {
      name: 'Total Banners',
      value: stats.totalBanners,
      href: '/admin/banners',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      color: 'bg-purple-500',
    },
    {
      name: 'Total Admins',
      value: stats.totalAdmins,
      href: '/admin/admins',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      color: 'bg-red-500',
    },
  ];

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="admin-card p-6">
          <h1 className="text-3xl font-bold admin-text-primary mb-2">
            Welcome to GG Catalog Store Admin
          </h1>
          <p className="text-gray-600">
            Manage your products, brands, categories, banners, and admin users from this dashboard.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {statCards.map((stat) => (
            <Link key={stat.name} href={stat.href}>
              <div className="admin-card p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer">
                <div className="flex items-center">
                  <div className={`${stat.color} p-3 rounded-lg text-white`}>
                    {stat.icon}
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <p className="text-2xl font-bold admin-text-primary">{stat.value}</p>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Recent Products */}
        <div className="admin-card">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium admin-text-primary">Recent Products</h2>
              <Link
                href="/admin/products"
                className="text-sm admin-text-accent hover:admin-text-accent-light"
              >
                View all
              </Link>
            </div>
          </div>
          <div className="p-6">
            {recentProducts.length > 0 ? (
              <div className="space-y-4">
                {recentProducts.map((product) => (
                  <div key={product.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        {product.photos && product.photos.length > 0 ? (
                          <img
                            className="h-10 w-10 rounded-lg object-cover"
                            src={product.photos[0].photo_url}
                            alt={product.name}
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                            <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="text-sm font-medium admin-text-primary">{product.name}</p>
                        <p className="text-sm text-gray-500">
                          {product.brand_name && `${product.brand_name} • `}
                          {product.category_name}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium admin-text-primary">
                        ${product.price.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {product.total_sold} sold
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No products</h3>
                <p className="mt-1 text-sm text-gray-500">Get started by creating a new product.</p>
                <div className="mt-6">
                  <Link
                    href="/admin/products"
                    className="admin-button-primary"
                  >
                    Add Product
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Link href="/admin/products" className="admin-card p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="text-center">
              <div className="admin-bg-accent p-3 rounded-lg inline-flex text-white mb-4">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-medium admin-text-primary">Add Product</h3>
              <p className="text-sm text-gray-500 mt-2">Create a new product listing</p>
            </div>
          </Link>

          <Link href="/admin/brands" className="admin-card p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="text-center">
              <div className="bg-blue-500 p-3 rounded-lg inline-flex text-white mb-4">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-medium admin-text-primary">Add Brand</h3>
              <p className="text-sm text-gray-500 mt-2">Create a new brand</p>
            </div>
          </Link>

          <Link href="/admin/categories" className="admin-card p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="text-center">
              <div className="bg-green-500 p-3 rounded-lg inline-flex text-white mb-4">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-medium admin-text-primary">Add Category</h3>
              <p className="text-sm text-gray-500 mt-2">Create a new category</p>
            </div>
          </Link>

          <Link href="/admin/banners" className="admin-card p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="text-center">
              <div className="bg-purple-500 p-3 rounded-lg inline-flex text-white mb-4">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-medium admin-text-primary">Add Banner</h3>
              <p className="text-sm text-gray-500 mt-2">Create a new banner</p>
            </div>
          </Link>
        </div>
      </div>
    </AdminLayout>
  );
};

export default withAdminAuth(AdminDashboardPage);
