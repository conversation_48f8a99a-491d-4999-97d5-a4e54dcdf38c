'use client';

import { useState, useCallback } from 'react';
import { Admin, AdminForm, AdminPresenter } from '@/types/admin';
import { adminApi } from '@/lib/adminApi';
import { useBasePresenter } from './useBasePresenter';
import { useWebhooks, triggerWebhookEvent } from '@/lib/webhooks';

export const useAdminPresenter = (): AdminPresenter => {
  const basePresenter = useBasePresenter();
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [currentAdmin, setCurrentAdmin] = useState<Admin | null>(null);

  // Set up webhook listeners for live updates
  useWebhooks([
    {
      type: 'admin_created',
      callback: (event) => {
        console.log('Admin created:', event.data);
        fetchAdmins(); // Refresh admins list
      }
    },
    {
      type: 'admin_deleted',
      callback: (event) => {
        console.log('Admin deleted:', event.data);
        setAdmins(prev => prev.filter(a => a.id !== event.data.id));
      }
    }
  ]);

  const fetchAdmins = useCallback(async (): Promise<void> => {
    await (basePresenter as any).withLoading(async () => {
      const response = await adminApi.getAll();
      if (response.success && response.data) {
        setAdmins(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch admins');
    });
  }, [basePresenter]);

  const createAdmin = useCallback(async (data: AdminForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await adminApi.create(data);
      if (response.success) {
        await fetchAdmins(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('admin_created', { id: response.id, username: data.username });
        return true;
      }
      throw new Error(response.error || 'Failed to create admin');
    });
    return result === true;
  }, [basePresenter, fetchAdmins]);

  const deleteAdmin = useCallback(async (id: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      // Note: The API doesn't have a delete admin endpoint, so we'll simulate it
      // In a real implementation, you would add this endpoint to the API
      throw new Error('Delete admin functionality not implemented in API');
    });
    return result === true;
  }, [basePresenter]);

  return {
    ...basePresenter,
    admins,
    currentAdmin,
    fetchAdmins,
    createAdmin,
    deleteAdmin,
    setCurrentAdmin,
  } as AdminPresenter & {
    setCurrentAdmin: (admin: Admin | null) => void;
  };
};
