'use client';

import { useState, useCallback } from 'react';
import { Brand, BrandForm, BrandPresenter } from '@/types/admin';
import { brandApi } from '@/lib/adminApi';
import { useBasePresenter } from './useBasePresenter';
import { useWebhooks, triggerWebhookEvent } from '@/lib/webhooks';

export const useBrandPresenter = (): BrandPresenter => {
  const basePresenter = useBasePresenter();
  const [brands, setBrands] = useState<Brand[]>([]);
  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);

  // Set up webhook listeners for live updates
  useWebhooks([
    {
      type: 'brand_created',
      callback: (event) => {
        console.log('Brand created:', event.data);
        fetchBrands(); // Refresh brands list
      }
    },
    {
      type: 'brand_updated',
      callback: (event) => {
        console.log('Brand updated:', event.data);
        fetchBrands(); // Refresh brands list
      }
    },
    {
      type: 'brand_deleted',
      callback: (event) => {
        console.log('Brand deleted:', event.data);
        setBrands(prev => prev.filter(b => b.id !== event.data.id));
      }
    }
  ]);

  const fetchBrands = useCallback(async (): Promise<void> => {
    await (basePresenter as any).withLoading(async () => {
      const response = await brandApi.getAll();
      if (response.success && response.data) {
        setBrands(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch brands');
    });
  }, [basePresenter]);

  const createBrand = useCallback(async (data: BrandForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await brandApi.create(data);
      if (response.success) {
        await fetchBrands(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('brand_created', { id: response.id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to create brand');
    });
    return result === true;
  }, [basePresenter, fetchBrands]);

  const updateBrand = useCallback(async (id: number, data: BrandForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await brandApi.update(id, data);
      if (response.success) {
        await fetchBrands(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('brand_updated', { id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to update brand');
    });
    return result === true;
  }, [basePresenter, fetchBrands]);

  const deleteBrand = useCallback(async (id: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await brandApi.delete(id);
      if (response.success) {
        setBrands(prev => prev.filter(b => b.id !== id));
        // Trigger webhook event for live updates
        triggerWebhookEvent('brand_deleted', { id });
        return true;
      }
      throw new Error(response.error || 'Failed to delete brand');
    });
    return result === true;
  }, [basePresenter]);

  const uploadBrandImage = useCallback(async (brandId: number, file: File): Promise<string | null> => {
    const result = await (basePresenter as any).withLoading(async () => {
      // Upload the image
      const uploadResponse = await brandApi.uploadImage(file);
      if (uploadResponse.success && uploadResponse.data) {
        // Associate the image with the brand
        const photoResponse = await brandApi.createOrUpdatePhoto(brandId, uploadResponse.data.url);
        if (photoResponse.success) {
          // Refresh the brands list to show the new image
          await fetchBrands();
          return uploadResponse.data.url;
        }
        throw new Error(photoResponse.error || 'Failed to associate image with brand');
      }
      throw new Error(uploadResponse.error || 'Failed to upload image');
    });
    return result;
  }, [basePresenter, fetchBrands]);

  const deleteBrandImage = useCallback(async (brandId: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await brandApi.deletePhoto(brandId);
      if (response.success) {
        await fetchBrands(); // Refresh the list
        return true;
      }
      throw new Error(response.error || 'Failed to delete brand image');
    });
    return result === true;
  }, [basePresenter, fetchBrands]);

  const getBrandById = useCallback(async (id: number): Promise<Brand | null> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await brandApi.getById(id);
      if (response.success && response.data) {
        setCurrentBrand(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch brand');
    });
    return result;
  }, [basePresenter]);

  return {
    ...basePresenter,
    brands,
    currentBrand,
    fetchBrands,
    createBrand,
    updateBrand,
    deleteBrand,
    uploadBrandImage,
    getBrandById,
    setCurrentBrand,
    deleteBrandImage,
  } as BrandPresenter & {
    getBrandById: (id: number) => Promise<Brand | null>;
    setCurrentBrand: (brand: Brand | null) => void;
    deleteBrandImage: (brandId: number) => Promise<boolean>;
  };
};
