'use client';

import React, { useEffect, useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { AdminRouteGuard } from '@/components/admin/AuthGuard';
import { useAdminPresenter } from '@/hooks/useAdminPresenter';
import { Admin, AdminForm } from '@/types/admin';
import {
  FormField,
  Input,
  Button,
  Modal,
  Table,
  Alert,
} from '@/components/admin/FormComponents';

const AdminsPage: React.FC = () => {
  const presenter = useAdminPresenter();
  const [showModal, setShowModal] = useState(false);
  const [formData, setFormData] = useState<AdminForm>({
    username: '',
    password: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    presenter.fetchAdmins();
  }, []);

  const resetForm = () => {
    setFormData({
      username: '',
      password: '',
    });
    setFormErrors({});
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.username.trim()) {
      errors.username = 'Username is required';
    }

    if (formData.username.length > 50) {
      errors.username = 'Username must be less than 50 characters';
    }

    if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      errors.username = 'Username can only contain letters, numbers, and underscores';
    }

    if (!formData.password) {
      errors.password = 'Password is required';
    }

    if (formData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters';
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      errors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const success = await presenter.createAdmin(formData);

    if (success) {
      closeModal();
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this admin? This action cannot be undone.')) {
      await presenter.deleteAdmin(id);
    }
  };

  const formatLastLogin = (admin: Admin): string => {
    if (!admin.last_login_at) {
      return 'Never';
    }
    return new Date(admin.last_login_at).toLocaleString();
  };

  const getIPAddress = (admin: Admin): string => {
    return admin.last_login_ip || 'Unknown';
  };

  return (
    <AdminRouteGuard>
      <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold admin-text-primary">Admin Management</h1>
          <Button onClick={openCreateModal}>
            Add Admin
          </Button>
        </div>

        {/* Error Alert */}
        {presenter.error && (
          <Alert
            type="error"
            message={presenter.error}
            onClose={() => presenter.setError(null)}
          />
        )}

        {/* Admins Table */}
        <div className="admin-card">
          {presenter.isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-600"></div>
            </div>
          ) : (
            <Table headers={['Username', 'Last Login', 'IP Address', 'Created', 'Actions']}>
              {presenter.admins.map((admin) => (
                <tr key={admin.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full admin-bg-accent flex items-center justify-center">
                          <span className="text-sm font-medium admin-text-secondary">
                            {admin.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium admin-text-primary">
                          {admin.username}
                        </div>
                        <div className="text-sm text-gray-500">
                          ID: {admin.id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatLastLogin(admin)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 font-mono">
                      {getIPAddress(admin)}
                    </div>
                    {admin.last_login_ip && (
                      <div className="text-xs text-gray-500">
                        Last login IP
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(admin.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => handleDelete(admin.id)}
                      disabled={presenter.admins.length <= 1}
                      title={presenter.admins.length <= 1 ? 'Cannot delete the last admin' : 'Delete admin'}
                    >
                      Delete
                    </Button>
                  </td>
                </tr>
              ))}
            </Table>
          )}

          {!presenter.isLoading && presenter.admins.length === 0 && (
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No admins</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by creating a new admin user.</p>
              <div className="mt-6">
                <Button onClick={openCreateModal}>
                  Add Admin
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Admin Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="admin-card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Admins
                  </dt>
                  <dd className="text-lg font-medium admin-text-primary">
                    {presenter.admins.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="admin-card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Sessions
                  </dt>
                  <dd className="text-lg font-medium admin-text-primary">
                    {presenter.admins.filter(admin => admin.last_login_at).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className="admin-card p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Security Level
                  </dt>
                  <dd className="text-lg font-medium admin-text-primary">
                    High
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Create Modal */}
        <Modal
          isOpen={showModal}
          onClose={closeModal}
          title="Create Admin"
          maxWidth="md"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <FormField label="Username" required error={formErrors.username}>
              <Input
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                placeholder="Enter username"
                error={!!formErrors.username}
              />
              <p className="text-xs text-gray-500 mt-1">
                Only letters, numbers, and underscores allowed
              </p>
            </FormField>

            <FormField label="Password" required error={formErrors.password}>
              <Input
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                placeholder="Enter password"
                error={!!formErrors.password}
              />
              <p className="text-xs text-gray-500 mt-1">
                Must be at least 8 characters with uppercase, lowercase, and number
              </p>
            </FormField>

            <div className="flex justify-end space-x-3">
              <Button type="button" variant="secondary" onClick={closeModal}>
                Cancel
              </Button>
              <Button type="submit" loading={presenter.isLoading}>
                Create Admin
              </Button>
            </div>
          </form>
        </Modal>
      </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
};

export default AdminsPage;
