'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/admin/login',
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { admin, isLoading } = useAdminAuth();

  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !admin) {
        // Store the attempted URL for redirect after login
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('redirectAfterLogin', pathname);
        }
        router.push(redirectTo);
      } else if (!requireAuth && admin) {
        // If user is already authenticated and trying to access login page
        router.push('/admin/dashboard');
      }
    }
  }, [admin, isLoading, requireAuth, router, redirectTo, pathname]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center admin-bg-primary">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-4 text-lg admin-text-primary">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render children if auth requirements aren't met
  if (requireAuth && !admin) {
    return null;
  }

  if (!requireAuth && admin) {
    return null;
  }

  return <>{children}</>;
};

export default AuthGuard;

// Higher-order component for protecting routes
export const withAuthGuard = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: Omit<AuthGuardProps, 'children'> = {}
): React.FC<P> => {
  const GuardedComponent: React.FC<P> = (props) => {
    return (
      <AuthGuard {...options}>
        <WrappedComponent {...props} />
      </AuthGuard>
    );
  };

  GuardedComponent.displayName = `withAuthGuard(${WrappedComponent.displayName || WrappedComponent.name})`;

  return GuardedComponent;
};

// Route-specific guards
export const AdminRouteGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <AuthGuard requireAuth={true} redirectTo="/admin/login">
    {children}
  </AuthGuard>
);

export const PublicRouteGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <AuthGuard requireAuth={false} redirectTo="/admin/dashboard">
    {children}
  </AuthGuard>
);

// Permission-based guard (for future use)
interface PermissionGuardProps {
  children: React.ReactNode;
  permissions?: string[];
  fallback?: React.ReactNode;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permissions = [],
  fallback = (
    <div className="text-center py-8">
      <div className="text-red-500 text-lg">Access Denied</div>
      <p className="text-gray-600 mt-2">You don't have permission to access this resource.</p>
    </div>
  ),
}) => {
  const { admin } = useAdminAuth();

  // For now, all authenticated admins have all permissions
  // In a real application, you would check admin.permissions against required permissions
  if (!admin) {
    return <>{fallback}</>;
  }

  // Future implementation would check permissions here
  // const hasPermission = permissions.every(permission => 
  //   admin.permissions?.includes(permission)
  // );
  
  // if (!hasPermission) {
  //   return <>{fallback}</>;
  // }

  return <>{children}</>;
};
