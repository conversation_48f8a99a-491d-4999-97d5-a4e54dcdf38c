'use client';

import { useState, useCallback } from 'react';
import { Banner, BannerForm, BannerPresenter } from '@/types/admin';
import { bannerApi } from '@/lib/adminApi';
import { useBasePresenter } from './useBasePresenter';
import { useWebhooks, triggerWebhookEvent } from '@/lib/webhooks';

export const useBannerPresenter = (): BannerPresenter => {
  const basePresenter = useBasePresenter();
  const [banners, setBanners] = useState<Banner[]>([]);
  const [currentBanner, setCurrentBanner] = useState<Banner | null>(null);

  // Set up webhook listeners for live updates
  useWebhooks([
    {
      type: 'banner_created',
      callback: (event) => {
        console.log('Banner created:', event.data);
        fetchBanners(); // Refresh banners list
      }
    },
    {
      type: 'banner_updated',
      callback: (event) => {
        console.log('Banner updated:', event.data);
        fetchBanners(); // Refresh banners list
      }
    },
    {
      type: 'banner_deleted',
      callback: (event) => {
        console.log('Banner deleted:', event.data);
        setBanners(prev => prev.filter(b => b.id !== event.data.id));
      }
    }
  ]);

  const fetchBanners = useCallback(async (): Promise<void> => {
    await (basePresenter as any).withLoading(async () => {
      const response = await bannerApi.getAll();
      if (response.success && response.data) {
        setBanners(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch banners');
    });
  }, [basePresenter]);

  const createBanner = useCallback(async (data: BannerForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await bannerApi.create(data);
      if (response.success) {
        await fetchBanners(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('banner_created', { id: response.id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to create banner');
    });
    return result === true;
  }, [basePresenter, fetchBanners]);

  const updateBanner = useCallback(async (id: number, data: BannerForm): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await bannerApi.update(id, data);
      if (response.success) {
        await fetchBanners(); // Refresh the list
        // Trigger webhook event for live updates
        triggerWebhookEvent('banner_updated', { id, ...data });
        return true;
      }
      throw new Error(response.error || 'Failed to update banner');
    });
    return result === true;
  }, [basePresenter, fetchBanners]);

  const deleteBanner = useCallback(async (id: number): Promise<boolean> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await bannerApi.delete(id);
      if (response.success) {
        setBanners(prev => prev.filter(b => b.id !== id));
        // Trigger webhook event for live updates
        triggerWebhookEvent('banner_deleted', { id });
        return true;
      }
      throw new Error(response.error || 'Failed to delete banner');
    });
    return result === true;
  }, [basePresenter]);

  const uploadBannerImage = useCallback(async (file: File): Promise<string | null> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const uploadResponse = await bannerApi.uploadImage(file);
      if (uploadResponse.success && uploadResponse.data) {
        return uploadResponse.data.url;
      }
      throw new Error(uploadResponse.error || 'Failed to upload banner image');
    });
    return result;
  }, [basePresenter]);

  const getBannerById = useCallback(async (id: number): Promise<Banner | null> => {
    const result = await (basePresenter as any).withLoading(async () => {
      const response = await bannerApi.getById(id);
      if (response.success && response.data) {
        setCurrentBanner(response.data);
        return response.data;
      }
      throw new Error(response.error || 'Failed to fetch banner');
    });
    return result;
  }, [basePresenter]);

  return {
    ...basePresenter,
    banners,
    currentBanner,
    fetchBanners,
    createBanner,
    updateBanner,
    deleteBanner,
    uploadBannerImage,
    getBannerById,
    setCurrentBanner,
  } as BannerPresenter & {
    getBannerById: (id: number) => Promise<Banner | null>;
    setCurrentBanner: (banner: Banner | null) => void;
  };
};
