'use client';

// Webhook utility for live updates in admin dashboard
export type WebhookEventType = 
  | 'product_created' 
  | 'product_updated' 
  | 'product_deleted'
  | 'brand_created' 
  | 'brand_updated' 
  | 'brand_deleted'
  | 'category_created' 
  | 'category_updated' 
  | 'category_deleted'
  | 'banner_created' 
  | 'banner_updated' 
  | 'banner_deleted'
  | 'admin_created' 
  | 'admin_deleted';

export interface WebhookEvent {
  type: WebhookEventType;
  data: any;
  timestamp: number;
}

export type WebhookCallback = (event: WebhookEvent) => void;

class WebhookManager {
  private listeners: Map<WebhookEventType, Set<WebhookCallback>> = new Map();
  private eventSource: EventSource | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    if (typeof window !== 'undefined') {
      this.connect();
    }
  }

  private connect() {
    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
      this.eventSource = new EventSource(`${baseUrl}/api/admin/events`, {
        withCredentials: true,
      });

      this.eventSource.onopen = () => {
        console.log('Webhook connection established');
        this.reconnectAttempts = 0;
      };

      this.eventSource.onmessage = (event) => {
        try {
          const webhookEvent: WebhookEvent = JSON.parse(event.data);
          this.handleEvent(webhookEvent);
        } catch (error) {
          console.error('Failed to parse webhook event:', error);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('Webhook connection error:', error);
        this.handleReconnect();
      };
    } catch (error) {
      console.error('Failed to establish webhook connection:', error);
      this.handleReconnect();
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
      
      setTimeout(() => {
        this.disconnect();
        this.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached. Webhook connection failed.');
    }
  }

  private handleEvent(event: WebhookEvent) {
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error('Error in webhook callback:', error);
        }
      });
    }
  }

  public subscribe(eventType: WebhookEventType, callback: WebhookCallback): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    
    const listeners = this.listeners.get(eventType)!;
    listeners.add(callback);

    // Return unsubscribe function
    return () => {
      listeners.delete(callback);
      if (listeners.size === 0) {
        this.listeners.delete(eventType);
      }
    };
  }

  public disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  // Simulate webhook events for development (since we don't have SSE implemented in the API yet)
  public simulateEvent(eventType: WebhookEventType, data: any) {
    const event: WebhookEvent = {
      type: eventType,
      data,
      timestamp: Date.now(),
    };
    this.handleEvent(event);
  }
}

// Singleton instance
const webhookManager = new WebhookManager();

export default webhookManager;

// React hook for using webhooks
export const useWebhook = (eventType: WebhookEventType, callback: WebhookCallback) => {
  React.useEffect(() => {
    const unsubscribe = webhookManager.subscribe(eventType, callback);
    return unsubscribe;
  }, [eventType, callback]);
};

// Hook for multiple webhook events
export const useWebhooks = (events: Array<{ type: WebhookEventType; callback: WebhookCallback }>) => {
  React.useEffect(() => {
    const unsubscribes = events.map(({ type, callback }) =>
      webhookManager.subscribe(type, callback)
    );

    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }, [events]);
};

// For development - simulate webhook events
export const simulateWebhookEvent = (eventType: WebhookEventType, data: any) => {
  webhookManager.simulateEvent(eventType, data);
};

// Trigger webhook events after successful operations
export const triggerWebhookEvent = (eventType: WebhookEventType, data: any) => {
  // In a real implementation, this would be handled by the server
  // For now, we'll simulate the event locally
  setTimeout(() => {
    webhookManager.simulateEvent(eventType, data);
  }, 100);
};

// Import React for the hook
import React from 'react';
