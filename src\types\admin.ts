// Admin Dashboard Types

export interface Admin {
  id: number;
  username: string;
  last_login_ip?: string;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Brand {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  photo?: BrandPhoto;
  brand_photo?: string; // For backward compatibility
}

export interface BrandPhoto {
  id: number;
  brand_id: number;
  photo_url: string;
  created_at: string;
}

export interface Category {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  photo?: CategoryPhoto;
  category_photo?: string; // For backward compatibility
}

export interface CategoryPhoto {
  id: number;
  category_id: number;
  photo_url: string;
  created_at: string;
}

export interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  brand_id?: number;
  category_id?: number;
  total_sold: number;
  avg_rating: number;
  total_raters: number;
  created_at: string;
  updated_at: string;
  brand_name?: string;
  category_name?: string;
  variants?: ProductVariant[];
  photos?: ProductPhoto[];
}

export interface ProductVariant {
  id: number;
  product_id: number;
  variant_name: string;
  price: number;
}

export interface ProductPhoto {
  id: number;
  product_id: number;
  photo_url: string;
}

export interface Banner {
  id: number;
  title?: string;
  description?: string;
  image_url?: string;
  banner_image_url?: string; // Database field name
  link_url?: string;
  redirect_url?: string; // Database field name
  is_active: boolean;
  active?: boolean; // Database field name
  created_at: string;
  updated_at: string;
}

export interface Rating {
  id: number;
  product_id: number;
  star: number;
  review_text?: string;
  created_at: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  username: string;
  password: string;
}

export interface AdminForm {
  username: string;
  password: string;
}

export interface BrandForm {
  name: string;
  description?: string;
}

export interface CategoryForm {
  name: string;
  description?: string;
}

export interface ProductForm {
  name: string;
  description?: string;
  price: number;
  brand_id?: number;
  category_id?: number;
}

export interface BannerForm {
  title?: string;
  description?: string;
  image_url?: string;
  link_url?: string;
  is_active: boolean;
}

export interface VariantForm {
  variant_name: string;
  price: number;
}

// Upload Types
export interface UploadResponse {
  success: boolean;
  data?: {
    filename: string;
    url: string;
    size: number;
    dimensions: {
      width: number;
      height: number;
    };
  };
  error?: string;
  message?: string;
}

export interface MultipleUploadResponse {
  success: boolean;
  data?: Array<{
    filename: string;
    url: string;
    size: number;
    dimensions: {
      width: number;
      height: number;
    };
  }>;
  error?: string;
  message?: string;
}

// Dashboard Stats
export interface DashboardStats {
  totalProducts: number;
  totalBrands: number;
  totalCategories: number;
  totalBanners: number;
  totalAdmins: number;
  recentProducts: Product[];
  recentBrands: Brand[];
  recentCategories: Category[];
}

// Authentication Context
export interface AuthContextType {
  admin: Admin | null;
  isLoading: boolean;
  login: (credentials: LoginForm) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

// MVP Presenter Types
export interface BasePresenter {
  isLoading: boolean;
  error: string | null;
  setError: (error: string | null) => void;
}

export interface AdminPresenter extends BasePresenter {
  admins: Admin[];
  currentAdmin: Admin | null;
  fetchAdmins: () => Promise<void>;
  createAdmin: (data: AdminForm) => Promise<boolean>;
  deleteAdmin: (id: number) => Promise<boolean>;
}

export interface BrandPresenter extends BasePresenter {
  brands: Brand[];
  currentBrand: Brand | null;
  fetchBrands: () => Promise<void>;
  createBrand: (data: BrandForm) => Promise<boolean>;
  updateBrand: (id: number, data: BrandForm) => Promise<boolean>;
  deleteBrand: (id: number) => Promise<boolean>;
  uploadBrandImage: (brandId: number, file: File) => Promise<string | null>;
}

export interface CategoryPresenter extends BasePresenter {
  categories: Category[];
  currentCategory: Category | null;
  fetchCategories: () => Promise<void>;
  createCategory: (data: CategoryForm) => Promise<boolean>;
  updateCategory: (id: number, data: CategoryForm) => Promise<boolean>;
  deleteCategory: (id: number) => Promise<boolean>;
  uploadCategoryImage: (categoryId: number, file: File) => Promise<string | null>;
}

export interface ProductPresenter extends BasePresenter {
  products: Product[];
  currentProduct: Product | null;
  fetchProducts: () => Promise<void>;
  createProduct: (data: ProductForm) => Promise<boolean>;
  updateProduct: (id: number, data: ProductForm) => Promise<boolean>;
  deleteProduct: (id: number) => Promise<boolean>;
  uploadProductImages: (productId: number, files: File[]) => Promise<string[]>;
  addVariant: (productId: number, data: VariantForm) => Promise<boolean>;
  updateVariant: (id: number, data: VariantForm) => Promise<boolean>;
  deleteVariant: (id: number) => Promise<boolean>;
}

export interface BannerPresenter extends BasePresenter {
  banners: Banner[];
  currentBanner: Banner | null;
  fetchBanners: () => Promise<void>;
  createBanner: (data: BannerForm) => Promise<boolean>;
  updateBanner: (id: number, data: BannerForm) => Promise<boolean>;
  deleteBanner: (id: number) => Promise<boolean>;
  uploadBannerImage: (file: File) => Promise<string | null>;
}
